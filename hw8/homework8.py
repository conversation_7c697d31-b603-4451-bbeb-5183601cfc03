############################################################
# CIS 521: Homework 8
############################################################

############################################################
# Imports
import random
import string
############################################################

# Include your imports here, if any are used.

############################################################

student_name = "<PERSON> Richards"

############################################################
# Section 1: Ngram Models
############################################################


def tokenize(text):
    tokens = []
    for word in text.split():
        contains_punctuation = False
        temp_token = ""
        for character in word:
            if character in string.punctuation:
                contains_punctuation = True
                if temp_token:
                    tokens.append(temp_token)
                tokens.append(character)
                temp_token = ""
            else:
                temp_token += character
        if not contains_punctuation:
            if word:
                tokens.append(word)
    return tokens


def ngrams(n, tokens):
    # The sentence should be padded with n−1 "<START>"
    # tokens at the beginning and a single "< END>"
    # token at the end.
    padded_tokens = ["<START>"] * (n - 1) + tokens + ["<END>"]
    n_grams = []
    for index in range(len(padded_tokens) - n + 1):
        # Each n-gram should consist of a 2-element tuple
        # (context, token), where the context is itself an
        # (n−1)-element tuple comprised of the n−1
        # words preceding the current token.
        context = tuple(padded_tokens[index: index + n - 1])
        token = padded_tokens[index + n - 1]
        n_grams.append((context, token))
    return n_grams


class NgramModel(object):

    def __init__(self, n):
        self.n = n
        self.ngram_counts = {}
        self.context_counts = {}

    def update(self, sentence):
        # Write a method update(self, sentence) which
        # computes the n-grams for the input sentence and
        # updates the internal counts.
        sentence_ngrams = ngrams(self.n, tokenize(sentence))
        for (context, token) in sentence_ngrams:
            if (context, token) not in self.ngram_counts:
                self.ngram_counts[(context, token)] = 0
            self.ngram_counts[(context, token)] += 1
            if context not in self.context_counts:
                self.context_counts[context] = 0
            self.context_counts[context] += 1

    def prob(self, context, token):
        # Write a method prob(self, context, token) which
        # accepts an (n−1)-tuple representing a context
        # and a token, and returns the probability of that
        # token occurring, given the preceding context.
        if context not in self.context_counts:
            return 0
        # Conditional probability =
        # Probability of token and context / probability of context
        probability = self.ngram_counts.get((context, token), 0)\
            / self.context_counts.get(context)
        return probability

    def random_token(self, context):
        possible_tokens = []
        for (loop_context, token) in self.ngram_counts:
            if loop_context == context:
                possible_tokens.append(token)
        if not possible_tokens:
            return None
        # Sort according to Python’s natural lexicographic ordering.
        possible_tokens.sort()

        cumulative_probabilities = []
        cumulative_sum = 0
        for token in possible_tokens:
            cumulative_sum += self.prob(context, token)
            cumulative_probabilities.append(cumulative_sum)

        # Let 0 ≤r < 1 be a random number between 0 and 1.
        r = random.random()
        # Return the first cumulative probility that r is less than.
        for index, cumulative_prob in enumerate(cumulative_probabilities):
            if r < cumulative_prob:
                return possible_tokens[index]


    def random_text(self, token_count):
        """
        Returns a string of space-separated tokens chosen at random using the random_token method.

        Args:
            token_count: Number of tokens to generate

        Returns:
            String of space-separated tokens
        """
        # Initialize starting context
        if self.n == 1:
            # For unigrams, context is always empty tuple
            starting_context = ()
        else:
            # For n-grams where n > 1, context is (n-1) "<START>" tokens
            starting_context = tuple(["<START>"] * (self.n - 1))

        context = starting_context
        tokens = []

        for _ in range(token_count):
            # Generate next token using current context
            token = self.random_token(context)

            # If no token can be generated, break
            if token is None:
                break

            tokens.append(token)

            # Check if we encountered "<END>" token
            if token == "<END>":
                # Reset context to starting context
                context = starting_context
            else:
                # Update context for next iteration
                if self.n == 1:
                    # For unigrams, context remains empty
                    context = ()
                else:
                    # For n-grams, slide the window: remove first element, add new token
                    context = context[1:] + (token,)

        return " ".join(tokens)

    def perplexity(self, sentence):
        pass


def create_ngram_model(n, path):
    pass

############################################################
# Section 2: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent about 6 hours on this assignment.
"""

feedback_question_2 = """
The most challenging part of this assignment was
implementing the `random_token()` method.
"""

feedback_question_3 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""
